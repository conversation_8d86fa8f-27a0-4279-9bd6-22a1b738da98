<template>
  <div class="h-full flex flex-col">
    <!-- 控制面板 -->
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-semibold text-gray-900 dark:text-white">地理位置分布图</h4>
      <div class="flex items-center space-x-2">
        <select
          v-model="selectedMetric"
          class="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1"
        >
          <option value="requests">请求数量</option>
          <option value="users">用户数量</option>
          <option value="errors">错误数量</option>
          <option value="latency">平均延迟</option>
        </select>
        <button
          @click="refreshData"
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
          title="刷新数据"
        >
          <component :is="RefreshCw" class="w-4 h-4" />
        </button>
        <button
          @click="toggleView"
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
          title="切换视图"
        >
          <component :is="viewMode === 'map' ? List : Globe" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- 地图视图 -->
    <div v-if="viewMode === 'map'" class="flex-1 relative bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden">
      <!-- 简化的世界地图 -->
      <div class="absolute inset-0 flex items-center justify-center">
        <svg viewBox="0 0 800 400" class="w-full h-full">
          <!-- 简化的世界地图轮廓 -->
          <g class="map-regions">
            <!-- 亚洲 -->
            <path
              d="M400 100 L600 80 L650 150 L580 200 L500 180 L450 120 Z"
              :fill="getRegionColor('asia')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('asia')"
            />

            <!-- 欧洲 -->
            <path
              d="M350 80 L450 70 L480 120 L420 140 L380 110 Z"
              :fill="getRegionColor('europe')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('europe')"
            />

            <!-- 北美洲 -->
            <path
              d="M100 80 L250 70 L280 150 L200 180 L120 140 Z"
              :fill="getRegionColor('north_america')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('north_america')"
            />

            <!-- 南美洲 -->
            <path
              d="M150 200 L250 190 L280 280 L200 320 L170 260 Z"
              :fill="getRegionColor('south_america')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('south_america')"
            />

            <!-- 非洲 -->
            <path
              d="M350 150 L450 140 L480 250 L420 300 L380 220 Z"
              :fill="getRegionColor('africa')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('africa')"
            />

            <!-- 大洋洲 -->
            <path
              d="M550 250 L650 240 L680 300 L600 320 L570 280 Z"
              :fill="getRegionColor('oceania')"
              :stroke="isDarkMode ? '#374151' : '#d1d5db'"
              stroke-width="1"
              class="cursor-pointer hover:opacity-80 transition-opacity"
              @click="selectRegion('oceania')"
            />
          </g>

          <!-- 数据点 -->
          <g class="data-points">
            <circle
              v-for="point in mapPoints"
              :key="point.id"
              :cx="point.x"
              :cy="point.y"
              :r="getPointRadius(point)"
              :fill="getPointColor(point)"
              :opacity="0.8"
              class="cursor-pointer hover:opacity-100 transition-opacity"
              @click="selectPoint(point)"
              @mouseenter="showTooltip(point, $event)"
              @mouseleave="hideTooltip"
            />
          </g>
        </svg>
      </div>

      <!-- 图例 -->
      <div class="absolute bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-lg">
        <h5 class="text-xs font-semibold text-gray-900 dark:text-white mb-2">{{ getMetricLabel() }}</h5>
        <div class="space-y-1">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">低</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">中</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">高</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto">
        <div class="space-y-2">
          <div
            v-for="region in regionData"
            :key="region.id"
            class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
            @click="selectRegion(region.id)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="text-2xl">{{ region.flag }}</div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-900 dark:text-white">{{ region.name }}</h5>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ region.cities.length }} 个城市</p>
                </div>
              </div>
              <div class="text-right">
                <div class="text-lg font-semibold text-gray-900 dark:text-white">
                  {{ formatValue(region.value) }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ getMetricLabel() }}
                </div>
              </div>
            </div>

            <!-- 城市详情 -->
            <div v-if="selectedRegion === region.id" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div class="grid grid-cols-2 gap-2">
                <div
                  v-for="city in region.cities"
                  :key="city.name"
                  class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
                >
                  <span class="text-xs text-gray-700 dark:text-gray-300">{{ city.name }}</span>
                  <span class="text-xs font-medium text-gray-900 dark:text-white">{{ formatValue(city.value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.show"
      class="fixed bg-black text-white text-xs rounded px-2 py-1 pointer-events-none z-50"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      <div class="font-semibold">{{ tooltip.title }}</div>
      <div>{{ getMetricLabel() }}: {{ formatValue(tooltip.value) }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue'
import { RefreshCw, Globe, List } from 'lucide-vue-next'
import { useAppStore } from '@/stores/appStore'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['regionClick', 'pointClick'])

const appStore = useAppStore()

// 组件状态
const selectedMetric = ref('requests')
const viewMode = ref('map')
const selectedRegion = ref('')
const mapPoints = ref([])
const regionData = ref([])

// 工具提示状态
const tooltip = reactive({
  show: false,
  x: 0,
  y: 0,
  title: '',
  value: 0
})

// 计算属性
const isDarkMode = computed(() => appStore.isDarkMode)

// 生成模拟数据
const generateMockData = () => {
  // 地图数据点
  const mockMapPoints = [
    { id: 'beijing', name: '北京', x: 520, y: 120, value: 15420, region: 'asia' },
    { id: 'shanghai', name: '上海', x: 540, y: 140, value: 12850, region: 'asia' },
    { id: 'tokyo', name: '东京', x: 580, y: 130, value: 18900, region: 'asia' },
    { id: 'seoul', name: '首尔', x: 560, y: 125, value: 9650, region: 'asia' },
    { id: 'london', name: '伦敦', x: 380, y: 100, value: 14200, region: 'europe' },
    { id: 'paris', name: '巴黎', x: 390, y: 105, value: 11800, region: 'europe' },
    { id: 'berlin', name: '柏林', x: 410, y: 95, value: 8900, region: 'europe' },
    { id: 'newyork', name: '纽约', x: 180, y: 110, value: 22500, region: 'north_america' },
    { id: 'losangeles', name: '洛杉矶', x: 120, y: 130, value: 16800, region: 'north_america' },
    { id: 'toronto', name: '多伦多', x: 200, y: 100, value: 7900, region: 'north_america' },
    { id: 'saopaulo', name: '圣保罗', x: 220, y: 250, value: 9200, region: 'south_america' },
    { id: 'buenosaires', name: '布宜诺斯艾利斯', x: 210, y: 280, value: 6500, region: 'south_america' },
    { id: 'cairo', name: '开罗', x: 420, y: 180, value: 5800, region: 'africa' },
    { id: 'capetown', name: '开普敦', x: 400, y: 280, value: 4200, region: 'africa' },
    { id: 'sydney', name: '悉尼', x: 620, y: 290, value: 8900, region: 'oceania' },
    { id: 'melbourne', name: '墨尔本', x: 610, y: 300, value: 6700, region: 'oceania' }
  ]

  // 根据选择的指标调整数值
  mapPoints.value = mockMapPoints.map(point => ({
    ...point,
    value: adjustValueByMetric(point.value)
  }))

  // 区域数据
  const mockRegionData = [
    {
      id: 'asia',
      name: '亚洲',
      flag: '🌏',
      value: mapPoints.value.filter(p => p.region === 'asia').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'asia')
    },
    {
      id: 'north_america',
      name: '北美洲',
      flag: '🌎',
      value: mapPoints.value.filter(p => p.region === 'north_america').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'north_america')
    },
    {
      id: 'europe',
      name: '欧洲',
      flag: '🌍',
      value: mapPoints.value.filter(p => p.region === 'europe').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'europe')
    },
    {
      id: 'south_america',
      name: '南美洲',
      flag: '🌎',
      value: mapPoints.value.filter(p => p.region === 'south_america').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'south_america')
    },
    {
      id: 'africa',
      name: '非洲',
      flag: '🌍',
      value: mapPoints.value.filter(p => p.region === 'africa').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'africa')
    },
    {
      id: 'oceania',
      name: '大洋洲',
      flag: '🌏',
      value: mapPoints.value.filter(p => p.region === 'oceania').reduce((sum, p) => sum + p.value, 0),
      cities: mapPoints.value.filter(p => p.region === 'oceania')
    }
  ]

  regionData.value = mockRegionData.sort((a, b) => b.value - a.value)
}

const adjustValueByMetric = (baseValue) => {
  switch (selectedMetric.value) {
    case 'users':
      return Math.floor(baseValue * 0.3)
    case 'errors':
      return Math.floor(baseValue * 0.05)
    case 'latency':
      return Math.floor(baseValue * 0.01) + 50
    case 'requests':
    default:
      return baseValue
  }
}

// 样式方法
const getRegionColor = (regionId) => {
  const region = regionData.value.find(r => r.id === regionId)
  if (!region) return isDarkMode.value ? '#374151' : '#e5e7eb'

  const maxValue = Math.max(...regionData.value.map(r => r.value))
  const intensity = region.value / maxValue

  if (intensity > 0.7) return '#ef4444'
  if (intensity > 0.4) return '#f59e0b'
  return '#10b981'
}

const getPointRadius = (point) => {
  const maxValue = Math.max(...mapPoints.value.map(p => p.value))
  const minRadius = 3
  const maxRadius = 12
  const ratio = point.value / maxValue
  return minRadius + (maxRadius - minRadius) * ratio
}

const getPointColor = (point) => {
  const maxValue = Math.max(...mapPoints.value.map(p => p.value))
  const intensity = point.value / maxValue

  if (intensity > 0.7) return '#ef4444'
  if (intensity > 0.4) return '#f59e0b'
  return '#10b981'
}

// 工具方法
const getMetricLabel = () => {
  const labels = {
    requests: '请求数量',
    users: '用户数量',
    errors: '错误数量',
    latency: '平均延迟(ms)'
  }
  return labels[selectedMetric.value] || '数值'
}

const formatValue = (value) => {
  if (selectedMetric.value === 'latency') {
    return `${value}ms`
  }
  return value.toLocaleString()
}

// 交互方法
const selectRegion = (regionId) => {
  selectedRegion.value = selectedRegion.value === regionId ? '' : regionId
  emit('regionClick', regionId)
}

const selectPoint = (point) => {
  emit('pointClick', point)
}

const showTooltip = (point, event) => {
  tooltip.show = true
  tooltip.x = event.clientX + 10
  tooltip.y = event.clientY - 10
  tooltip.title = point.name
  tooltip.value = point.value
}

const hideTooltip = () => {
  tooltip.show = false
}

const refreshData = () => {
  generateMockData()
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'map' ? 'list' : 'map'
}

// 监听器
watch(selectedMetric, () => {
  generateMockData()
})

// 生命周期
onMounted(() => {
  generateMockData()
})
</script>
