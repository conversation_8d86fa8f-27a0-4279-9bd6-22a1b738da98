<template>
  <div
    class="virtual-terminal h-full flex flex-col font-mono transition-all duration-300"
    :style="terminalStyles"
  >
    <!-- 终端头部 -->
    <div
      class="terminal-header flex items-center justify-between p-3 border-b shadow-lg transition-all duration-300"
      :style="headerStyles"
    >
      <div class="flex items-center space-x-4">
        <!-- 终端控制按钮 -->
        <div class="flex space-x-2">
          <button
            @click="minimizeTerminal"
            class="w-3 h-3 rounded-full bg-red-500 hover:bg-red-400 transition-colors cursor-pointer shadow-sm"
            title="最小化"
          ></button>
          <button
            @click="maximizeTerminal"
            class="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-400 transition-colors cursor-pointer shadow-sm"
            title="最大化"
          ></button>
          <button
            @click="toggleFullscreen"
            class="w-3 h-3 rounded-full bg-green-500 hover:bg-green-400 transition-colors cursor-pointer shadow-sm"
            title="全屏"
          ></button>
        </div>

        <div class="h-4 w-px bg-gray-600"></div>

        <div class="flex items-center space-x-3">
          <component :is="Terminal" class="w-4 h-4 text-green-400" />
          <span class="text-gray-200 text-sm font-medium">实时日志终端</span>

          <!-- 连接状态指示器 -->
          <div class="flex items-center space-x-2 px-2 py-1 bg-gray-700 rounded-full">
            <div
              class="w-2 h-2 rounded-full relative"
              :class="isConnected ? 'bg-green-500' : 'bg-red-500'"
            >
              <div
                v-if="isConnected"
                class="absolute inset-0 w-2 h-2 rounded-full bg-green-500 animate-ping opacity-75"
              ></div>
            </div>
            <span class="text-xs text-gray-300 font-medium">
              {{ isConnected ? '实时连接' : '连接断开' }}
            </span>
          </div>

          <!-- 性能指示器 -->
          <div class="flex items-center space-x-3 text-xs text-gray-400">
            <div class="flex items-center space-x-1">
              <component :is="Activity" class="w-3 h-3" />
              <span>{{ logRate }}/s</span>
            </div>
            <div class="flex items-center space-x-1">
              <component :is="Clock" class="w-3 h-3" />
              <span>{{ latency }}ms</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- 终端主题切换 -->
        <div class="flex items-center space-x-1 bg-gray-700 rounded-lg p-1">
          <button
            v-for="theme in terminalThemes"
            :key="theme.name"
            @click="setTerminalTheme(theme)"
            class="px-2 py-1 text-xs rounded transition-colors"
            :class="currentTheme.name === theme.name
              ? 'bg-gray-600 text-white'
              : 'text-gray-400 hover:text-gray-200'"
            :title="theme.label"
          >
            {{ theme.label }}
          </button>
        </div>

        <!-- 终端控制按钮组 -->
        <div class="flex items-center space-x-1 bg-gray-700 rounded-lg p-1">
          <button
            @click="toggleAutoScroll"
            class="p-2 rounded transition-colors tooltip-container"
            :class="autoScroll ? 'text-green-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="自动滚动 (Ctrl+A)"
          >
            <component :is="ArrowDown" class="w-4 h-4" />
          </button>
          <button
            @click="toggleWordWrap"
            class="p-2 rounded transition-colors"
            :class="wordWrap ? 'text-green-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="自动换行 (Ctrl+W)"
          >
            <component :is="WrapText" class="w-4 h-4" />
          </button>
          <button
            @click="toggleLineNumbers"
            class="p-2 rounded transition-colors"
            :class="showLineNumbers ? 'text-green-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="显示行号 (Ctrl+L)"
          >
            <component :is="Hash" class="w-4 h-4" />
          </button>
          <button
            @click="toggleTimestamps"
            class="p-2 rounded transition-colors"
            :class="showTimestamps ? 'text-green-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="显示时间戳 (Ctrl+T)"
          >
            <component :is="Clock" class="w-4 h-4" />
          </button>
          <button
            @click="toggleFollowMode"
            class="p-2 rounded transition-colors"
            :class="followMode ? 'text-yellow-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="跟随模式 (Ctrl+F)"
          >
            <component :is="Target" class="w-4 h-4" />
          </button>
          <button
            @click="pauseTerminal"
            class="p-2 rounded transition-colors"
            :class="isPaused ? 'text-orange-400 bg-gray-600' : 'text-gray-400 hover:text-gray-200 hover:bg-gray-600'"
            title="暂停/恢复 (Space)"
          >
            <component :is="isPaused ? Play : Pause" class="w-4 h-4" />
          </button>
          <button
            @click="clearTerminal"
            class="p-2 rounded transition-colors text-gray-400 hover:text-red-400 hover:bg-gray-600"
            title="清空终端 (Ctrl+C)"
          >
            <component :is="Trash2" class="w-4 h-4" />
          </button>
        </div>

        <!-- 统计信息 -->
        <div class="flex items-center space-x-3 text-xs text-gray-400 bg-gray-700 rounded-lg px-3 py-2">
          <div class="flex items-center space-x-1">
            <component :is="FileText" class="w-3 h-3" />
            <span>{{ formatNumber(logLines.length) }} 行</span>
          </div>
          <div class="h-3 w-px bg-gray-600"></div>
          <div class="flex items-center space-x-1">
            <component :is="HardDrive" class="w-3 h-3" />
            <span>{{ formatBytes(terminalSize) }}</span>
          </div>
        </div>

        <!-- 连接控制 -->
        <button
          @click="toggleConnection"
          class="px-4 py-2 text-xs rounded-lg border-2 transition-all duration-200 font-medium"
          :class="isConnected
            ? 'border-red-500 text-red-400 hover:bg-red-500 hover:text-white hover:shadow-lg hover:shadow-red-500/25'
            : 'border-green-500 text-green-400 hover:bg-green-500 hover:text-white hover:shadow-lg hover:shadow-green-500/25'"
        >
          <component :is="isConnected ? Wifi : WifiOff" class="w-3 h-3 mr-1 inline" />
          {{ isConnected ? '断开连接' : '连接服务' }}
        </button>
      </div>
    </div>
    
    <!-- 过滤器栏 -->
    <div
      class="filter-bar p-3 border-b transition-all duration-300"
      :style="filterBarStyles"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <div class="relative">
            <component :is="Search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              v-model="filterText"
              type="text"
              placeholder="搜索日志内容... (支持正则表达式)"
              class="bg-gray-700 text-gray-200 pl-10 pr-4 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 focus:outline-none transition-all duration-200 w-80"
              @keyup.enter="applyFilter"
              @keyup.escape="clearFilter"
            />
            <div v-if="filterText" class="absolute right-2 top-1/2 transform -translate-y-1/2">
              <button
                @click="clearFilter"
                class="text-gray-400 hover:text-gray-200 transition-colors"
              >
                <component :is="X" class="w-4 h-4" />
              </button>
            </div>
          </div>

          <!-- 快速过滤器 -->
          <div class="flex items-center space-x-2">
            <button
              v-for="quickFilter in quickFilters"
              :key="quickFilter.key"
              @click="applyQuickFilter(quickFilter)"
              class="px-3 py-1 text-xs rounded-full border transition-all duration-200"
              :class="activeQuickFilter === quickFilter.key
                ? 'border-green-500 bg-green-500/20 text-green-400'
                : 'border-gray-600 text-gray-400 hover:border-gray-500 hover:text-gray-300'"
            >
              {{ quickFilter.label }}
            </button>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- 级别过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-xs text-gray-400">级别:</label>
            <select
              v-model="selectedLevel"
              class="bg-gray-700 text-gray-300 px-3 py-1 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none transition-colors"
            >
              <option value="">全部</option>
              <option value="ERROR" class="text-red-400">ERROR</option>
              <option value="WARN" class="text-yellow-400">WARN</option>
              <option value="INFO" class="text-blue-400">INFO</option>
              <option value="DEBUG" class="text-gray-400">DEBUG</option>
            </select>
          </div>

          <!-- 来源过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-xs text-gray-400">来源:</label>
            <select
              v-model="selectedSource"
              class="bg-gray-700 text-gray-300 px-3 py-1 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none transition-colors"
            >
              <option value="">全部</option>
              <option v-for="source in availableSources" :key="source" :value="source">
                {{ source }}
              </option>
            </select>
          </div>

          <!-- 统计信息 -->
          <div class="flex items-center space-x-3 text-xs bg-gray-700 rounded-lg px-3 py-2">
            <div class="flex items-center space-x-1 text-gray-300">
              <component :is="Eye" class="w-3 h-3" />
              <span>显示: <span class="text-green-400 font-medium">{{ formatNumber(filteredLines.length) }}</span></span>
            </div>
            <div class="h-3 w-px bg-gray-600"></div>
            <div class="flex items-center space-x-1 text-gray-400">
              <span>总计: {{ formatNumber(logLines.length) }}</span>
            </div>
          </div>

          <!-- 过滤器操作 -->
          <div class="flex items-center space-x-1">
            <button
              @click="showAdvancedFilters = !showAdvancedFilters"
              class="p-2 rounded-lg transition-colors"
              :class="showAdvancedFilters ? 'text-blue-400 bg-gray-600' : 'text-gray-400 hover:text-blue-400 hover:bg-gray-700'"
              title="高级过滤器"
            >
              <component :is="Settings" class="w-4 h-4" />
            </button>
            <button
              @click="exportLogs"
              class="p-2 text-gray-400 hover:text-purple-400 hover:bg-gray-700 rounded-lg transition-colors"
              title="导出日志 (Ctrl+E)"
            >
              <component :is="Download" class="w-4 h-4" />
            </button>
            <button
              @click="saveCurrentFilter"
              class="p-2 text-gray-400 hover:text-green-400 hover:bg-gray-700 rounded-lg transition-colors"
              title="保存当前过滤器 (Ctrl+S)"
            >
              <component :is="Save" class="w-4 h-4" />
            </button>
            <button
              @click="resetAllFilters"
              class="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg transition-colors"
              title="重置所有过滤器 (Ctrl+R)"
            >
              <component :is="RotateCcw" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- 高级过滤器面板 -->
      <div v-if="showAdvancedFilters" class="advanced-filters bg-gray-800 border-t border-gray-700 p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 时间范围过滤 -->
          <div>
            <label class="block text-xs text-gray-400 mb-2">时间范围</label>
            <select class="w-full bg-gray-700 text-gray-300 px-3 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none">
              <option value="">全部时间</option>
              <option value="5m">最近5分钟</option>
              <option value="15m">最近15分钟</option>
              <option value="1h">最近1小时</option>
              <option value="6h">最近6小时</option>
              <option value="24h">最近24小时</option>
            </select>
          </div>

          <!-- IP地址过滤 -->
          <div>
            <label class="block text-xs text-gray-400 mb-2">IP地址</label>
            <input
              type="text"
              placeholder="输入IP地址"
              class="w-full bg-gray-700 text-gray-300 px-3 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none"
            />
          </div>

          <!-- 状态码过滤 -->
          <div>
            <label class="block text-xs text-gray-400 mb-2">HTTP状态码</label>
            <select class="w-full bg-gray-700 text-gray-300 px-3 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none">
              <option value="">全部状态码</option>
              <option value="2xx">2xx 成功</option>
              <option value="3xx">3xx 重定向</option>
              <option value="4xx">4xx 客户端错误</option>
              <option value="5xx">5xx 服务器错误</option>
            </select>
          </div>

          <!-- 响应时间过滤 -->
          <div>
            <label class="block text-xs text-gray-400 mb-2">响应时间 (ms)</label>
            <div class="flex space-x-2">
              <input
                type="number"
                placeholder="最小"
                class="flex-1 bg-gray-700 text-gray-300 px-3 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none"
              />
              <input
                type="number"
                placeholder="最大"
                class="flex-1 bg-gray-700 text-gray-300 px-3 py-2 rounded-lg text-sm border border-gray-600 focus:border-green-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- 高级过滤器操作 -->
        <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-700">
          <div class="flex items-center space-x-2">
            <button class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors">
              应用过滤器
            </button>
            <button class="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors">
              重置
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors">
              保存为预设
            </button>
            <button class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors">
              加载预设
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 终端内容区域 -->
    <div
      ref="terminalContent"
      class="terminal-content flex-1 overflow-hidden relative transition-all duration-300"
      :style="contentStyles"
      @scroll="handleScroll"
    >
      <!-- 虚拟滚动容器 -->
      <div 
        class="virtual-scroll-container"
        :style="{ height: `${virtualHeight}px` }"
      >
        <!-- 可见行容器 -->
        <div 
          class="visible-lines"
          :style="{ transform: `translateY(${offsetY}px)` }"
        >
          <div
            v-for="(line, index) in visibleLines"
            :key="line.id"
            class="log-line flex items-start py-2 px-4 hover:bg-gray-800/50 transition-all duration-150 group border-l-2 border-transparent"
            :class="getLineClass(line)"
            @click="selectLine(line)"
            @dblclick="showLineDetails(line)"
          >
            <!-- 行号 -->
            <div
              v-if="showLineNumbers"
              class="line-number text-gray-600 text-xs w-16 flex-shrink-0 text-right mr-4 select-none font-mono"
            >
              {{ (startIndex + index + 1).toString().padStart(6, ' ') }}
            </div>

            <!-- 时间戳 -->
            <div
              v-if="showTimestamps"
              class="timestamp text-gray-500 text-xs w-24 flex-shrink-0 mr-4 font-mono"
            >
              {{ formatTimestamp(line.timestamp) }}
            </div>

            <!-- 日志级别标签 -->
            <div class="level-container flex-shrink-0 mr-4">
              <span
                class="level-badge px-2 py-1 text-xs font-bold rounded-md border"
                :class="typeof getLevelBadgeClass(line.level) === 'string' ? getLevelBadgeClass(line.level) : ''"
                :style="typeof getLevelBadgeClass(line.level) === 'object' ? getLevelBadgeClass(line.level) : {}"
              >
                {{ line.level || 'INFO' }}
              </span>
            </div>

            <!-- 来源标签 -->
            <div class="source-container flex-shrink-0 mr-4">
              <span class="source-badge px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded-md border border-gray-600 font-mono">
                {{ line.source || 'unknown' }}
              </span>
            </div>

            <!-- 日志内容 -->
            <div class="content-container flex-1 min-w-0">
              <div
                class="content text-sm leading-relaxed"
                :class="{
                  'whitespace-pre-wrap': wordWrap,
                  'truncate': !wordWrap
                }"
                :style="{ color: currentTheme.colors?.text || '#10b981' }"
                v-html="highlightText(line.message || line.raw)"
              ></div>

              <!-- 元数据标签 -->
              <div v-if="line.metadata && Object.keys(line.metadata).length > 0" class="metadata-tags mt-2 flex flex-wrap gap-1">
                <span
                  v-if="line.metadata.ips && line.metadata.ips.length > 0"
                  class="metadata-tag px-2 py-1 text-xs bg-blue-900/30 text-blue-300 rounded border border-blue-700"
                >
                  <component :is="Globe" class="w-3 h-3 mr-1 inline" />
                  {{ line.metadata.ips[0] }}
                </span>
                <span
                  v-if="line.metadata.httpStatus"
                  class="metadata-tag px-2 py-1 text-xs rounded border"
                  :class="getHttpStatusClass(line.metadata.httpStatus)"
                >
                  {{ line.metadata.httpStatus }}
                </span>
                <span
                  v-if="line.metadata.responseTime"
                  class="metadata-tag px-2 py-1 text-xs bg-yellow-900/30 text-yellow-300 rounded border border-yellow-700"
                >
                  <component :is="Clock" class="w-3 h-3 mr-1 inline" />
                  {{ line.metadata.responseTime }}ms
                </span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="actions opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center space-x-1 ml-4">
              <button
                @click.stop="copyLine(line)"
                class="action-btn p-2 rounded-lg hover:bg-gray-700 text-gray-500 hover:text-green-400 transition-colors"
                title="复制日志"
              >
                <component :is="Copy" class="w-4 h-4" />
              </button>
              <button
                @click.stop="showContext(line)"
                class="action-btn p-2 rounded-lg hover:bg-gray-700 text-gray-500 hover:text-blue-400 transition-colors"
                title="查看上下文"
              >
                <component :is="Eye" class="w-4 h-4" />
              </button>
              <button
                @click.stop="addBookmark(line)"
                class="action-btn p-2 rounded-lg hover:bg-gray-700 text-gray-500 hover:text-yellow-400 transition-colors"
                title="添加书签"
              >
                <component :is="Bookmark" class="w-4 h-4" />
              </button>
              <button
                @click.stop="shareLine(line)"
                class="action-btn p-2 rounded-lg hover:bg-gray-700 text-gray-500 hover:text-purple-400 transition-colors"
                title="分享日志"
              >
                <component :is="Share2" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 滚动到底部按钮 -->
      <div 
        v-if="!isAtBottom && logLines.length > 0"
        class="absolute bottom-4 right-4"
      >
        <button
          @click="scrollToBottom"
          class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-full shadow-lg transition-colors flex items-center space-x-2"
        >
          <component :is="ArrowDown" class="w-4 h-4" />
          <span class="text-sm">{{ newLinesCount > 0 ? `${newLinesCount} 新` : '底部' }}</span>
        </button>
      </div>
    </div>
    
    <!-- 状态栏 -->
    <div
      class="status-bar p-3 border-t text-xs transition-all duration-300"
      :style="statusBarStyles"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <!-- 连接状态 -->
          <div class="flex items-center space-x-2">
            <div class="flex items-center space-x-1">
              <component :is="isConnected ? Wifi : WifiOff" class="w-3 h-3" :class="isConnected ? 'text-green-400' : 'text-red-400'" />
              <span class="text-gray-300 font-medium">{{ connectionStatus }}</span>
            </div>
          </div>

          <!-- 性能指标 -->
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-1">
              <component :is="Activity" class="w-3 h-3 text-blue-400" />
              <span class="text-gray-400">速率:</span>
              <span class="text-blue-400 font-medium">{{ logRate }}/秒</span>
            </div>
            <div class="flex items-center space-x-1">
              <component :is="Clock" class="w-3 h-3 text-yellow-400" />
              <span class="text-gray-400">延迟:</span>
              <span class="text-yellow-400 font-medium">{{ latency }}ms</span>
            </div>
            <div class="flex items-center space-x-1">
              <component :is="Zap" class="w-3 h-3 text-purple-400" />
              <span class="text-gray-400">CPU:</span>
              <span class="text-purple-400 font-medium">{{ cpuUsage }}%</span>
            </div>
          </div>

          <!-- 过滤状态 -->
          <div v-if="hasActiveFilters" class="flex items-center space-x-2 px-2 py-1 bg-green-900/30 rounded border border-green-700">
            <component :is="Filter" class="w-3 h-3 text-green-400" />
            <span class="text-green-400 font-medium">过滤器已激活</span>
          </div>
        </div>

        <div class="flex items-center space-x-6">
          <!-- 内存使用 -->
          <div class="flex items-center space-x-1">
            <component :is="HardDrive" class="w-3 h-3 text-orange-400" />
            <span class="text-gray-400">内存:</span>
            <span class="text-orange-400 font-medium">{{ formatBytes(memoryUsage) }}</span>
          </div>

          <!-- 终端主题 -->
          <div class="flex items-center space-x-1">
            <component :is="Palette" class="w-3 h-3 text-indigo-400" />
            <span class="text-gray-400">主题:</span>
            <span class="text-indigo-400 font-medium">{{ currentTheme.label }}</span>
          </div>

          <!-- 当前时间 -->
          <div class="flex items-center space-x-1">
            <component :is="Clock" class="w-3 h-3 text-gray-400" />
            <span class="text-gray-300 font-mono">{{ currentTime }}</span>
          </div>

          <!-- 快捷键提示 -->
          <button
            @click="showShortcutsHelp = !showShortcutsHelp"
            class="flex items-center space-x-1 px-2 py-1 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded transition-colors"
            title="快捷键帮助"
          >
            <component :is="HelpCircle" class="w-3 h-3" />
            <span>快捷键</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 快捷键帮助弹窗 -->
    <ShortcutsHelp
      :show="showShortcutsHelp"
      @close="showShortcutsHelp = false"
    />

    <!-- 日志详情弹窗 -->
    <LogLineDetails
      :show="showLogDetails"
      :log-line="selectedLogLine"
      @close="showLogDetails = false"
    />

    <!-- 主题管理器 -->
    <TerminalThemeManager
      :show="showThemeManager"
      :current-theme="currentTheme"
      @close="showThemeManager = false"
      @theme-change="handleThemeChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { parseLogLine, filterLogs, aggregateLogs } from '@/utils/logParser'
import {
  ArrowDown,
  WrapText,
  Trash2,
  Filter,
  Copy,
  Eye,
  Search,
  Settings,
  Download,
  Bookmark,
  Terminal,
  Activity,
  Clock,
  Hash,
  FileText,
  HardDrive,
  Wifi,
  WifiOff,
  X,
  RotateCcw,
  Globe,
  Share2,
  Zap,
  Palette,
  HelpCircle,
  Target,
  Play,
  Pause,
  Save,
  Upload,
  Monitor,
  AlertTriangle,
  CheckCircle,
  Info,
  XCircle
} from 'lucide-vue-next'

import ShortcutsHelp from './ShortcutsHelp.vue'
import LogLineDetails from './LogLineDetails.vue'
import TerminalThemeManager from './TerminalThemeManager.vue'

const appStore = useAppStore()

// 终端状态
const isConnected = ref(false)
const autoScroll = ref(true)
const wordWrap = ref(false)
const showLineNumbers = ref(true)
const showTimestamps = ref(true)
const followMode = ref(false)
const isPaused = ref(false)
const filterText = ref('')
const selectedLevel = ref('')
const selectedSource = ref('')
const activeQuickFilter = ref('')
const showShortcutsHelp = ref(false)
const showLogDetails = ref(false)
const selectedLogLine = ref(null)
const showThemeManager = ref(false)
const isFullscreen = ref(false)
const showAdvancedFilters = ref(false)

// 终端主题
const terminalThemes = [
  {
    name: 'dark',
    label: '暗黑经典',
    colors: {
      background: '#1f2937',
      text: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6',
      border: '#374151',
      headerBg: '#111827',
      contentBg: '#1f2937'
    }
  },
  {
    name: 'matrix',
    label: '黑客帝国',
    colors: {
      background: '#000000',
      text: '#00ff00',
      error: '#ff0000',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#003300',
      headerBg: '#001100',
      contentBg: '#000000'
    }
  },
  {
    name: 'ocean',
    label: '深海蓝调',
    colors: {
      background: '#1e3a8a',
      text: '#93c5fd',
      error: '#fca5a5',
      warning: '#fcd34d',
      info: '#a7f3d0',
      border: '#1e40af',
      headerBg: '#1e40af',
      contentBg: '#1e3a8a'
    }
  },
  {
    name: 'sunset',
    label: '日落余晖',
    colors: {
      background: '#9a3412',
      text: '#fed7aa',
      error: '#fecaca',
      warning: '#fef3c7',
      info: '#bfdbfe',
      border: '#c2410c',
      headerBg: '#c2410c',
      contentBg: '#9a3412'
    }
  },
  {
    name: 'cyberpunk',
    label: '赛博朋克',
    colors: {
      background: '#0f0f23',
      text: '#ff00ff',
      error: '#ff073a',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#ff00ff40',
      headerBg: '#1a1a2e',
      contentBg: '#0f0f23'
    }
  },
  { name: 'manage', label: '管理...', colors: null }
]
const currentTheme = ref(terminalThemes[0])

// 快速过滤器
const quickFilters = [
  { key: 'errors', label: '错误', level: 'ERROR' },
  { key: 'warnings', label: '警告', level: 'WARN' },
  { key: 'recent', label: '最近5分钟', timeRange: 5 },
  { key: 'nginx', label: 'Nginx', source: 'nginx' },
  { key: 'mysql', label: 'MySQL', source: 'mysql' }
]

// 日志数据
const logLines = ref([])
const terminalContent = ref(null)
const selectedLine = ref(null)

// 虚拟滚动
const itemHeight = 28 // 每行高度
const visibleCount = ref(50) // 可见行数
const scrollTop = ref(0)
const startIndex = ref(0)
const endIndex = ref(50)

// 性能监控
const logRate = ref(0)
const latency = ref(0)
const memoryUsage = ref(0)
const cpuUsage = ref(0)
const currentTime = ref('')
const terminalSize = ref(0)
const newLinesCount = ref(0)

// WebSocket连接
let ws = null
let logRateInterval = null
let statusInterval = null
let logInterval = null
let logBuffer = []

const availableSources = computed(() => {
  const sources = new Set(logLines.value.map(line => line.source))
  return Array.from(sources).sort()
})

const filteredLines = computed(() => {
  return logLines.value.filter(line => {
    if (filterText.value && !line.message.toLowerCase().includes(filterText.value.toLowerCase())) {
      return false
    }
    if (selectedLevel.value && line.level !== selectedLevel.value) {
      return false
    }
    if (selectedSource.value && line.source !== selectedSource.value) {
      return false
    }
    return true
  })
})

const virtualHeight = computed(() => {
  return filteredLines.value.length * itemHeight
})

const offsetY = computed(() => {
  return startIndex.value * itemHeight
})

const visibleLines = computed(() => {
  return filteredLines.value.slice(startIndex.value, endIndex.value)
})

const isAtBottom = computed(() => {
  if (!terminalContent.value) return true
  const { scrollTop, scrollHeight, clientHeight } = terminalContent.value
  return scrollTop + clientHeight >= scrollHeight - 10
})

const connectionStatus = computed(() => {
  if (isConnected.value) return '实时连接'
  return '连接断开'
})

const hasActiveFilters = computed(() => {
  return filterText.value || selectedLevel.value || selectedSource.value || activeQuickFilter.value
})

const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 主题样式计算属性
const terminalStyles = computed(() => {
  if (!currentTheme.value?.colors) return {}

  const colors = currentTheme.value.colors
  return {
    backgroundColor: colors.background,
    color: colors.text,
    background: `linear-gradient(135deg, ${colors.background} 0%, ${colors.contentBg} 100%)`
  }
})

const headerStyles = computed(() => {
  if (!currentTheme.value?.colors) return {}

  const colors = currentTheme.value.colors
  return {
    background: `linear-gradient(135deg, ${colors.headerBg} 0%, ${colors.background} 100%)`,
    borderBottomColor: colors.border,
    backdropFilter: 'blur(10px)'
  }
})

const filterBarStyles = computed(() => {
  if (!currentTheme.value?.colors) return {}

  const colors = currentTheme.value.colors
  return {
    background: `linear-gradient(135deg, ${colors.headerBg}90 0%, ${colors.background}90 100%)`,
    borderBottomColor: colors.border,
    backdropFilter: 'blur(8px)'
  }
})

const contentStyles = computed(() => {
  if (!currentTheme.value?.colors) return {}

  const colors = currentTheme.value.colors
  return {
    background: `linear-gradient(180deg, ${colors.contentBg}95 0%, ${colors.background}95 100%)`
  }
})

const statusBarStyles = computed(() => {
  if (!currentTheme.value?.colors) return {}

  const colors = currentTheme.value.colors
  return {
    background: `linear-gradient(135deg, ${colors.headerBg}95 0%, ${colors.background}95 100%)`,
    borderTopColor: colors.border,
    backdropFilter: 'blur(10px)'
  }
})

// 生成真实格式的日志数据
const generateLogLine = () => {
  const logFormats = [
    // Nginx访问日志
    () => {
      const ips = ['*************', '*********', '***********', '************']
      const methods = ['GET', 'POST', 'PUT', 'DELETE']
      const urls = ['/api/users', '/api/orders', '/api/products', '/login', '/dashboard']
      const statuses = [200, 201, 400, 401, 404, 500]
      const userAgents = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'curl/7.68.0']

      const ip = ips[Math.floor(Math.random() * ips.length)]
      const method = methods[Math.floor(Math.random() * methods.length)]
      const url = urls[Math.floor(Math.random() * urls.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const size = Math.floor(Math.random() * 10000) + 100
      const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)]

      const timestamp = format(new Date(), 'dd/MMM/yyyy:HH:mm:ss xxxx')
      return `${ip} - - [${timestamp}] "${method} ${url} HTTP/1.1" ${status} ${size} "-" "${userAgent}"`
    },

    // 应用程序日志
    () => {
      const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']
      const threads = ['main', 'worker-1', 'worker-2', 'scheduler']
      const messages = [
        'Database connection established successfully',
        'User authentication failed for user: admin',
        'Cache miss for key: user_session_12345',
        'API request processed in 245ms',
        'Memory usage exceeded threshold: 85%',
        'SSL certificate will expire in 30 days',
        'Backup process completed successfully',
        'Rate limit exceeded for client',
        'Database query took 1.2 seconds to complete',
        'Configuration file reloaded'
      ]

      const level = levels[Math.floor(Math.random() * levels.length)]
      const thread = threads[Math.floor(Math.random() * threads.length)]
      const message = messages[Math.floor(Math.random() * messages.length)]

      const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss.SSS')
      return `${timestamp} [${level}] [${thread}] ${message}`
    },

    // JSON格式日志
    () => {
      const levels = ['error', 'warn', 'info', 'debug']
      const services = ['nginx', 'mysql', 'redis', 'api-server', 'auth-service']
      const messages = [
        'Request processed successfully',
        'Database connection timeout',
        'Authentication failed',
        'Cache operation completed',
        'Service health check passed'
      ]

      return JSON.stringify({
        timestamp: new Date().toISOString(),
        level: levels[Math.floor(Math.random() * levels.length)],
        service: services[Math.floor(Math.random() * services.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        requestId: `req_${Math.random().toString(36).substr(2, 9)}`,
        userId: Math.random() > 0.5 ? `user_${Math.floor(Math.random() * 1000)}` : null,
        responseTime: Math.floor(Math.random() * 1000) + 50,
        metadata: {
          ip: '192.168.1.' + Math.floor(Math.random() * 255),
          userAgent: 'Mozilla/5.0 (compatible)'
        }
      })
    }
  ]

  // 随机选择一种日志格式
  const formatGenerator = logFormats[Math.floor(Math.random() * logFormats.length)]
  const rawLog = formatGenerator()

  // 使用日志解析器解析
  const parsedLog = parseLogLine(rawLog)

  return {
    ...parsedLog,
    id: Date.now() + Math.random(),
    raw: rawLog
  }
}

// 模拟WebSocket连接
const connectWebSocket = () => {
  isConnected.value = true

  // 清理之前的连接
  if (logInterval) {
    clearInterval(logInterval)
  }

  // 模拟实时日志流
  logInterval = setInterval(() => {
    if (!isConnected.value) {
      clearInterval(logInterval)
      logInterval = null
      return
    }

    // 随机生成1-5条日志
    const count = Math.floor(Math.random() * 5) + 1
    for (let i = 0; i < count; i++) {
      const newLine = generateLogLine()
      logBuffer.push(newLine)
    }

    // 批量处理日志
    if (logBuffer.length > 0) {
      processLogBuffer()
    }
  }, Math.random() * 2000 + 500) // 500-2500ms间隔
}

const processLogBuffer = () => {
  const newLines = [...logBuffer]
  logBuffer = []
  
  logLines.value.push(...newLines)
  newLinesCount.value += newLines.length
  
  // 限制日志行数，避免内存溢出
  if (logLines.value.length > 10000) {
    logLines.value.splice(0, logLines.value.length - 10000)
  }
  
  // 更新终端大小
  terminalSize.value = JSON.stringify(logLines.value).length
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

const toggleConnection = () => {
  if (isConnected.value) {
    disconnectWebSocket()
  } else {
    connectWebSocket()
  }
}

const disconnectWebSocket = () => {
  isConnected.value = false
  if (ws) {
    ws.close()
    ws = null
  }
  if (logInterval) {
    clearInterval(logInterval)
    logInterval = null
  }
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
  if (autoScroll.value) {
    scrollToBottom()
  }
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const toggleTimestamps = () => {
  showTimestamps.value = !showTimestamps.value
}

const toggleFollowMode = () => {
  followMode.value = !followMode.value
  if (followMode.value) {
    autoScroll.value = true
    scrollToBottom()
  }
}

const pauseTerminal = () => {
  isPaused.value = !isPaused.value
  if (isPaused.value) {
    appStore.addNotification({
      type: 'warning',
      message: '终端已暂停，新日志将被缓存'
    })
  } else {
    appStore.addNotification({
      type: 'success',
      message: '终端已恢复'
    })
  }
}

const minimizeTerminal = () => {
  appStore.addNotification({
    type: 'info',
    message: '终端已最小化'
  })
}

const maximizeTerminal = () => {
  appStore.addNotification({
    type: 'info',
    message: '终端已最大化'
  })
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const clearTerminal = () => {
  if (confirm('确定要清空所有日志吗？此操作不可撤销。')) {
    logLines.value = []
    newLinesCount.value = 0
    terminalSize.value = 0
    scrollTop.value = 0
    startIndex.value = 0
    endIndex.value = visibleCount.value

    appStore.addNotification({
      type: 'success',
      message: '终端已清空'
    })
  }
}

const handleScroll = (event) => {
  const { scrollTop: newScrollTop, clientHeight } = event.target
  scrollTop.value = newScrollTop
  
  // 计算可见范围
  const newStartIndex = Math.floor(newScrollTop / itemHeight)
  const newEndIndex = Math.min(
    newStartIndex + Math.ceil(clientHeight / itemHeight) + 5,
    filteredLines.value.length
  )
  
  startIndex.value = Math.max(0, newStartIndex - 5)
  endIndex.value = newEndIndex
  
  // 重置新行计数
  if (isAtBottom.value) {
    newLinesCount.value = 0
  }
}

const scrollToBottom = () => {
  if (terminalContent.value) {
    terminalContent.value.scrollTop = terminalContent.value.scrollHeight
    newLinesCount.value = 0
  }
}

const selectLine = (line) => {
  selectedLine.value = line
}

const showLineDetails = (line) => {
  selectedLogLine.value = line
  showLogDetails.value = true
}

const copyLine = (line) => {
  const text = `${formatTimestamp(line.timestamp)} [${line.level}] ${line.source}: ${line.message}`
  navigator.clipboard.writeText(text)
  appStore.addNotification({
    type: 'success',
    message: '日志行已复制到剪贴板'
  })
}

const showContext = (line) => {
  appStore.addNotification({
    type: 'info',
    message: `查看上下文: ${line.id}`
  })
}

const getLineClass = (line) => {
  const classes = ['cursor-pointer']
  
  if (selectedLine.value?.id === line.id) {
    classes.push('bg-blue-900')
  }
  
  if (line.level === 'ERROR') {
    classes.push('border-l-2 border-red-500')
  } else if (line.level === 'WARN') {
    classes.push('border-l-2 border-yellow-500')
  }
  
  return classes
}

const getLevelClass = (level) => {
  const classes = {
    ERROR: 'text-red-400',
    WARN: 'text-yellow-400',
    INFO: 'text-blue-400',
    DEBUG: 'text-gray-400'
  }
  return classes[level] || 'text-gray-400'
}

const getLevelBadgeClass = (level) => {
  if (!currentTheme.value?.colors) {
    const classes = {
      ERROR: 'bg-red-900/50 text-red-300 border-red-700',
      WARN: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',
      INFO: 'bg-blue-900/50 text-blue-300 border-blue-700',
      DEBUG: 'bg-gray-900/50 text-gray-300 border-gray-700'
    }
    return classes[level] || classes.INFO
  }

  // 使用主题颜色
  const colors = currentTheme.value.colors
  const levelColors = {
    ERROR: colors.error,
    WARN: colors.warning,
    INFO: colors.info,
    DEBUG: colors.text
  }

  const color = levelColors[level] || levelColors.INFO
  return {
    backgroundColor: color + '20',
    color: color,
    borderColor: color + '60'
  }
}

const getHttpStatusClass = (status) => {
  if (status >= 500) return 'bg-red-900/30 text-red-300 border-red-700'
  if (status >= 400) return 'bg-yellow-900/30 text-yellow-300 border-yellow-700'
  if (status >= 200 && status < 300) return 'bg-green-900/30 text-green-300 border-green-700'
  return 'bg-gray-900/30 text-gray-300 border-gray-700'
}

const highlightText = (text) => {
  if (!filterText.value) return text
  
  const regex = new RegExp(`(${filterText.value})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-400 text-black">$1</mark>')
}

const formatTimestamp = (timestamp) => {
  return format(timestamp, 'HH:mm:ss')
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听过滤器变化
watch([filterText, selectedLevel, selectedSource], () => {
  startIndex.value = 0
  endIndex.value = visibleCount.value
  if (terminalContent.value) {
    terminalContent.value.scrollTop = 0
  }
})

// 键盘快捷键处理
const handleKeydown = (event) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key.toLowerCase()) {
      case 'a':
        event.preventDefault()
        toggleAutoScroll()
        break
      case 'w':
        event.preventDefault()
        toggleWordWrap()
        break
      case 'l':
        event.preventDefault()
        toggleLineNumbers()
        break
      case 't':
        event.preventDefault()
        toggleTimestamps()
        break
      case 'f':
        event.preventDefault()
        toggleFollowMode()
        break
      case 'c':
        event.preventDefault()
        clearTerminal()
        break
      case 'k':
        event.preventDefault()
        document.querySelector('.filter-bar input')?.focus()
        break
      case 's':
        event.preventDefault()
        exportLogs()
        break
      case 'e':
        event.preventDefault()
        exportLogs()
        break
      case 'r':
        event.preventDefault()
        resetAllFilters()
        break
      case 'p':
        event.preventDefault()
        pauseTerminal()
        break
    }
  } else {
    switch (event.key) {
      case ' ':
        event.preventDefault()
        pauseTerminal()
        break
      case 'Escape':
        clearFilter()
        break
      case 'End':
        scrollToBottom()
        break
      case 'Home':
        scrollToTop()
        break
    }
  }
}

const scrollToTop = () => {
  if (terminalContent.value) {
    terminalContent.value.scrollTop = 0
  }
}

const exportLogs = () => {
  const logs = filteredLines.value.map(line => ({
    timestamp: line.timestamp,
    level: line.level,
    source: line.source,
    message: line.message
  }))

  const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `terminal-logs-${format(new Date(), 'yyyy-MM-dd-HH-mm-ss')}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  appStore.addNotification({
    type: 'success',
    message: `已导出 ${logs.length} 条日志`
  })
}

const updateVisibleCount = () => {
  if (terminalContent.value) {
    const newVisibleCount = Math.ceil(terminalContent.value.clientHeight / itemHeight) + 10
    visibleCount.value = newVisibleCount
    endIndex.value = Math.min(startIndex.value + newVisibleCount, filteredLines.value.length)
  }
}

onMounted(() => {
  try {
    console.log('VirtualTerminal mounted, 当前主题:', currentTheme.value)

    // 计算可见行数
    if (terminalContent.value) {
      visibleCount.value = Math.ceil(terminalContent.value.clientHeight / itemHeight) + 10
      endIndex.value = visibleCount.value
    }

    // 启动性能监控
    statusInterval = setInterval(() => {
      try {
        currentTime.value = format(new Date(), 'HH:mm:ss')
        memoryUsage.value = Math.random() * 100000000 + 50000000
        cpuUsage.value = Math.floor(Math.random() * 30) + 10
        latency.value = Math.floor(Math.random() * 50) + 10
      } catch (error) {
        console.error('性能监控错误:', error)
      }
    }, 1000)

    // 启动日志速率监控
    logRateInterval = setInterval(() => {
      try {
        logRate.value = Math.floor(Math.random() * 20) + 5
      } catch (error) {
        console.error('日志速率监控错误:', error)
      }
    }, 1000)

    // 自动连接
    connectWebSocket()

    // 监听键盘事件
    document.addEventListener('keydown', handleKeydown)

    // 监听窗口大小变化
    window.addEventListener('resize', updateVisibleCount)
  } catch (error) {
    console.error('VirtualTerminal mounted 错误:', error)
  }
})






const setTerminalTheme = (theme) => {
  if (theme.name === 'manage') {
    showThemeManager.value = true
    return
  }

  // 确保主题有完整的颜色信息
  if (theme.colors) {
    currentTheme.value = theme
    appStore.addNotification({
      type: 'success',
      message: `已切换到${theme.label}主题`
    })
  }
}

const handleThemeChange = (theme) => {
  console.log('主题切换:', theme)
  currentTheme.value = theme
  showThemeManager.value = false
  appStore.addNotification({
    type: 'success',
    message: `已应用${theme.label}主题`
  })
}

const applyQuickFilter = (filter) => {
  // 清除其他过滤器
  filterText.value = ''
  selectedLevel.value = ''
  selectedSource.value = ''

  // 应用快速过滤器
  if (filter.level) {
    selectedLevel.value = filter.level
  }
  if (filter.source) {
    selectedSource.value = filter.source
  }
  if (filter.timeRange) {
    // 这里可以实现时间范围过滤逻辑
  }

  activeQuickFilter.value = filter.key

  appStore.addNotification({
    type: 'success',
    message: `已应用${filter.label}过滤器`
  })
}

const clearFilter = () => {
  filterText.value = ''
}

const applyFilter = () => {
  // 过滤逻辑已在computed中实现
}

const resetAllFilters = () => {
  filterText.value = ''
  selectedLevel.value = ''
  selectedSource.value = ''
  activeQuickFilter.value = ''

  appStore.addNotification({
    type: 'success',
    message: '已重置所有过滤器'
  })
}

const saveCurrentFilter = () => {
  appStore.addNotification({
    type: 'success',
    message: '当前过滤器已保存'
  })
}

const addBookmark = (line) => {
  appStore.addNotification({
    type: 'success',
    message: '书签已添加'
  })
}

const shareLine = (line) => {
  const text = `${formatTimestamp(line.timestamp)} [${line.level}] ${line.source}: ${line.message}`
  navigator.clipboard.writeText(text)
  appStore.addNotification({
    type: 'success',
    message: '日志链接已复制到剪贴板'
  })
}

onUnmounted(() => {
  disconnectWebSocket()
  if (statusInterval) {
    clearInterval(statusInterval)
    statusInterval = null
  }
  if (logRateInterval) {
    clearInterval(logRateInterval)
    logRateInterval = null
  }
  if (logInterval) {
    clearInterval(logInterval)
    logInterval = null
  }

  // 清理事件监听器
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', updateVisibleCount)
})
</script>

<style scoped>
.virtual-terminal {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.terminal-header {
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
}

.filter-bar {
  backdrop-filter: blur(8px);
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.9) 100%);
}

.terminal-content {
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(180deg, rgba(17, 24, 39, 0.95) 0%, rgba(0, 0, 0, 0.95) 100%);
}

.terminal-content::-webkit-scrollbar {
  width: 12px;
}

.terminal-content::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 6px;
}

.terminal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #6b7280 0%, #4b5563 100%);
  border-radius: 6px;
  border: 2px solid rgba(55, 65, 81, 0.3);
}

.terminal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
}

.log-line {
  min-height: 40px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  transition: all 0.15s ease;
  position: relative;
}

.log-line:hover {
  background: linear-gradient(90deg, rgba(55, 65, 81, 0.4) 0%, rgba(75, 85, 99, 0.2) 100%) !important;
  border-left-color: #10b981 !important;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-line.selected {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.1) 100%) !important;
  border-left-color: #3b82f6 !important;
  border-left-width: 3px;
}

.log-line.error {
  border-left-color: #ef4444;
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, transparent 100%);
}

.log-line.warning {
  border-left-color: #f59e0b;
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, transparent 100%);
}

.level-badge {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.level-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.source-badge {
  transition: all 0.2s ease;
  font-weight: 500;
}

.source-badge:hover {
  background-color: #4b5563 !important;
  transform: translateY(-1px);
}

.metadata-tag {
  transition: all 0.2s ease;
  font-weight: 500;
}

.metadata-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.action-btn {
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-bar {
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
  border-top: 1px solid rgba(75, 85, 99, 0.3);
}

mark {
  border-radius: 3px;
  padding: 2px 4px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1f2937;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-line {
  animation: slideIn 0.3s ease-out;
}

/* 连接状态指示器动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 主题变量 */
.theme-dark {
  --terminal-bg: #1f2937;
  --terminal-text: #10b981;
}

.theme-matrix {
  --terminal-bg: #000000;
  --terminal-text: #00ff00;
}

.theme-ocean {
  --terminal-bg: #1e3a8a;
  --terminal-text: #93c5fd;
}

.theme-sunset {
  --terminal-bg: #9a3412;
  --terminal-text: #fed7aa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-line {
    padding: 8px 12px;
    min-height: 36px;
  }

  .level-badge,
  .source-badge {
    font-size: 10px;
    padding: 2px 6px;
  }

  .metadata-tag {
    font-size: 10px;
    padding: 1px 4px;
  }

  .action-btn {
    padding: 6px;
  }
}

/* 打印样式 */
@media print {
  .virtual-terminal {
    background: white !important;
    color: black !important;
  }

  .log-line {
    border-bottom: 1px solid #ccc !important;
    background: white !important;
  }

  .level-badge {
    border: 1px solid #ccc !important;
    background: white !important;
    color: black !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .log-line {
    border-bottom: 2px solid #fff;
  }

  .level-badge,
  .source-badge,
  .metadata-tag {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .log-line,
  .level-badge,
  .source-badge,
  .metadata-tag,
  .action-btn {
    transition: none;
    animation: none;
  }
}
</style>
