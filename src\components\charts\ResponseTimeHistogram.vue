<template>
  <div class="h-full flex flex-col">
    <!-- 控制面板 -->
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-semibold text-gray-900 dark:text-white">响应时间分布图</h4>
      <div class="flex items-center space-x-2">
        <select
          v-model="selectedTimeRange"
          class="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1"
        >
          <option value="1h">最近1小时</option>
          <option value="6h">最近6小时</option>
          <option value="24h">最近24小时</option>
          <option value="7d">最近7天</option>
        </select>
        <button
          @click="refreshData"
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
          title="刷新数据"
        >
          <component :is="RefreshCw" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="flex-1">
      <Bar
        :data="chartData"
        :options="chartOptions"
      />
    </div>

    <!-- 统计信息 -->
    <div class="mt-4 grid grid-cols-4 gap-4 text-center">
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ avgResponseTime }}ms</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">平均响应时间</div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ p95ResponseTime }}ms</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">P95响应时间</div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ p99ResponseTime }}ms</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">P99响应时间</div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ maxResponseTime }}ms</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">最大响应时间</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Bar } from 'vue-chartjs'
import { RefreshCw } from 'lucide-vue-next'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

// 组件状态
const selectedTimeRange = ref('24h')
const histogramData = ref([])

// 生成模拟数据
const generateMockData = () => {
  const buckets = [
    { range: '0-50ms', count: 1250, color: '#10b981' },
    { range: '50-100ms', count: 2100, color: '#3b82f6' },
    { range: '100-200ms', count: 1800, color: '#8b5cf6' },
    { range: '200-500ms', count: 950, color: '#f59e0b' },
    { range: '500-1000ms', count: 420, color: '#ef4444' },
    { range: '1000-2000ms', count: 180, color: '#dc2626' },
    { range: '2000ms+', count: 45, color: '#991b1b' }
  ]

  histogramData.value = buckets
}

// 计算属性
const chartData = computed(() => ({
  labels: histogramData.value.map(bucket => bucket.range),
  datasets: [
    {
      label: '请求数量',
      data: histogramData.value.map(bucket => bucket.count),
      backgroundColor: histogramData.value.map(bucket => bucket.color + '80'),
      borderColor: histogramData.value.map(bucket => bucket.color),
      borderWidth: 2,
      borderRadius: 4,
      borderSkipped: false
    }
  ]
}))

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#374151',
      borderWidth: 1,
      callbacks: {
        label: (context) => {
          const total = histogramData.value.reduce((sum, bucket) => sum + bucket.count, 0)
          const percentage = ((context.parsed.y / total) * 100).toFixed(1)
          return `请求数: ${context.parsed.y.toLocaleString()} (${percentage}%)`
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '响应时间范围',
        color: '#6b7280'
      },
      grid: {
        display: false
      },
      ticks: {
        color: '#6b7280'
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '请求数量',
        color: '#6b7280'
      },
      beginAtZero: true,
      grid: {
        color: 'rgba(156, 163, 175, 0.2)'
      },
      ticks: {
        color: '#6b7280',
        callback: function(value) {
          return value.toLocaleString()
        }
      }
    }
  },
  elements: {
    bar: {
      borderRadius: 4
    }
  }
}))

// 统计计算
const avgResponseTime = computed(() => {
  // 模拟计算平均响应时间
  const bucketMidpoints = [25, 75, 150, 350, 750, 1500, 3000]
  let totalTime = 0
  let totalCount = 0

  histogramData.value.forEach((bucket, index) => {
    totalTime += bucketMidpoints[index] * bucket.count
    totalCount += bucket.count
  })

  return totalCount > 0 ? Math.round(totalTime / totalCount) : 0
})

const p95ResponseTime = computed(() => {
  // 模拟P95计算
  const totalCount = histogramData.value.reduce((sum, bucket) => sum + bucket.count, 0)
  const p95Count = totalCount * 0.95

  let cumulativeCount = 0
  for (let i = 0; i < histogramData.value.length; i++) {
    cumulativeCount += histogramData.value[i].count
    if (cumulativeCount >= p95Count) {
      const bucketMidpoints = [25, 75, 150, 350, 750, 1500, 3000]
      return bucketMidpoints[i]
    }
  }
  return 0
})

const p99ResponseTime = computed(() => {
  // 模拟P99计算
  const totalCount = histogramData.value.reduce((sum, bucket) => sum + bucket.count, 0)
  const p99Count = totalCount * 0.99

  let cumulativeCount = 0
  for (let i = 0; i < histogramData.value.length; i++) {
    cumulativeCount += histogramData.value[i].count
    if (cumulativeCount >= p99Count) {
      const bucketMidpoints = [25, 75, 150, 350, 750, 1500, 3000]
      return bucketMidpoints[i]
    }
  }
  return 0
})

const maxResponseTime = computed(() => {
  // 找到最后一个非零的bucket
  for (let i = histogramData.value.length - 1; i >= 0; i--) {
    if (histogramData.value[i].count > 0) {
      const bucketMaxValues = [50, 100, 200, 500, 1000, 2000, 5000]
      return bucketMaxValues[i]
    }
  }
  return 0
})

// 方法
const refreshData = () => {
  generateMockData()
}

// 监听器
watch(selectedTimeRange, () => {
  generateMockData()
})

// 生命周期
onMounted(() => {
  generateMockData()
})
</script>
